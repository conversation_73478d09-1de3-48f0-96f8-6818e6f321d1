import { createApi } from "@reduxjs/toolkit/query/react";
import { baseQueryWithInterceptor as baseQuery } from "../instance";

export const inventoryDataApiSlice = createApi({
  reducerPath: "inventoryDataApiSlice",
  baseQuery,
  tagTypes: ["inventoryData"],
  endpoints: (builder) => ({
    getInventoryList: builder.query({
      query: (body) => ({
        url: `inventory/listInventory`,
        method: "POST",
        body,
      }),
      providesTags: ["inventoryData"],
    }),
    deleteInventory: builder.mutation({
      query: (body) => ({
        url: `inventory/deleteInventory`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["inventoryData"],
    }),
    createInventory: builder.mutation({
      query: (body) => ({
        url: `inventory/createInventory`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["inventoryData"],
    }),
    editInventory: builder.mutation({
      query: (body) => ({
        url: `inventory/editInventory`,
        method: "POST",
        body,
      }),
      invalidatesTags: ["inventoryData"],
    }),
    // singleProduct: builder.mutation({
    //   query: (body) => ({
    //     url: `inventorys/singleProduct`,
    //     method: "POST",
    //     body,
    //   }),
    // }),
  }),
});

export const {
  useGetInventoryListQuery,
//   useListAllProductQuery,
  useDeleteInventoryMutation,
  useCreateInventoryMutation,
  useEditInventoryMutation,
//   useSingleProductMutation,
} = inventoryDataApiSlice;
